# Doc Web Deployment Guide

## Overview

This guide provides detailed instructions for hosting the `doc_web` Node.js application on the same EC2 instance as the Java+AngularJS `lm-app`. The doc_web application serves API documentation by rendering API Blueprint (.apib) files into HTML format.

## Current Architecture

### Java+AngularJS App (lm-app)
- **Framework**: Play Framework 2.4
- **Runtime**: JVM with 2-4GB RAM allocation
- **Port**: Configurable via `${PORT}` environment variable
- **Deployment Path**: `/lavomat/deploys/prod/`
- **Startup Script**: `/lavomat/scripts/APP.sh`

### Doc Web App
- **Framework**: Node.js + Express
- **Dependencies**: aglio, ejs, express, dotenv
- **Default Port**: 3000 (configurable via `PORT` env var)
- **Purpose**: Renders API Blueprint files to HTML documentation
- **Resource Requirements**: ~50-100MB RAM, minimal CPU

## Deployment Options

### Option 1: Different Ports (Simple Setup)

**Configuration:**
- Java app: Port 80 (current)
- doc_web: Port 3000

**Access URLs:**
- Main app: `http://your-domain.com`
- Documentation: `http://your-domain.com:3000`

**Pros:** Simple to implement, no additional software needed
**Cons:** Users need to remember port numbers

### Option 2: Reverse Proxy with Nginx (Recommended)

**Configuration:**
- Nginx: Port 80/443 (public)
- Java app: Port 9000 (internal)
- doc_web: Port 3000 (internal)

**URL Routing:**
- `/docs/*` → doc_web application
- `/*` → Java+AngularJS application

**Access URLs:**
- Main app: `http://your-domain.com`
- Documentation: `http://your-domain.com/docs`

**Pros:** Clean URLs, professional setup, SSL termination, load balancing capability
**Cons:** Requires Nginx installation and configuration

### Option 3: Subdomain Setup

**Configuration:**
- Java app: `app.your-domain.com`
- doc_web: `docs.your-domain.com`

**Pros:** Clear separation, easy to remember
**Cons:** Requires DNS configuration

## Implementation Guide (Option 2 - Recommended)

### Step 1: Install Node.js

```bash
# Connect to EC2 instance
ssh ec2-user@your-instance

# Install Node.js (if not already installed)
curl -fsSL https://rpm.nodesource.com/setup_16.x | sudo bash -
sudo yum install -y nodejs

# Verify installation
node --version
npm --version
```

### Step 2: Deploy doc_web Application

```bash
# Create deployment directory
sudo mkdir -p /lavomat/doc_web
sudo chown lavomat:lavomat /lavomat/doc_web

# Switch to lavomat user
sudo su - lavomat

# Clone or copy doc_web files to deployment directory
cd /lavomat/doc_web

# Install dependencies
npm install

# Create production environment file
cp .env.example .env
echo "PORT=3000" > .env

# Create dist directory and copy documentation files
mkdir -p dist
cp -a ../lm-app/doc/*.apib ./dist/
```

### Step 3: Install and Configure Nginx

```bash
# Install Nginx
sudo yum install -y nginx

# Create Nginx configuration
sudo tee /etc/nginx/conf.d/lavomat.conf << 'EOF'
server {
    listen 80;
    server_name your-domain.com;

    # Documentation routes
    location /docs {
        proxy_pass http://localhost:3000;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection 'upgrade';
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_cache_bypass $http_upgrade;
    }

    # Main application routes
    location / {
        proxy_pass http://localhost:9000;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection 'upgrade';
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_cache_bypass $http_upgrade;
    }
}
EOF

# Test Nginx configuration
sudo nginx -t

# Enable and start Nginx
sudo systemctl enable nginx
sudo systemctl start nginx
```

### Step 4: Create Startup Scripts

```bash
# Create doc_web startup script
sudo tee /lavomat/scripts/DOC_WEB.sh << 'EOF'
#!/bin/sh

CMD=$1

start() {
    echo "Starting Doc Web app..."
    cd /lavomat/doc_web
    nohup node index.js >> /var/log/lavomat/doc_web.log 2>&1 &
    echo $! > /var/run/doc_web.pid
    echo "Doc Web app started"
}

stop() {
    echo "Stopping Doc Web app..."
    if [ -f /var/run/doc_web.pid ]; then
        PID=$(cat /var/run/doc_web.pid)
        kill $PID
        rm -f /var/run/doc_web.pid
        echo "Doc Web app stopped"
    else
        echo "Doc Web app is not running"
    fi
}

status() {
    if [ -f /var/run/doc_web.pid ]; then
        PID=$(cat /var/run/doc_web.pid)
        if ps -p $PID > /dev/null; then
            echo "Doc Web app is running (PID: $PID)"
        else
            echo "Doc Web app is not running (stale PID file)"
            rm -f /var/run/doc_web.pid
        fi
    else
        echo "Doc Web app is not running"
    fi
}

case "$CMD" in
    start)
        start
        ;;
    stop)
        stop
        ;;
    restart)
        stop
        start
        ;;
    status)
        status
        ;;
    *)
        echo "Usage $0 {start|stop|restart|status}"
        RETVAL=1
esac

exit $RETVAL
EOF

# Make script executable
sudo chmod +x /lavomat/scripts/DOC_WEB.sh
```

### Step 5: Update Java App Configuration

```bash
# Update Java app to use port 9000 instead of 80
# Modify the APP.sh script to include -Dhttp.port=9000
sudo sed -i 's/-J-Xmx[0-9]*M/-J-Xmx2048M -Dhttp.port=9000/g' /lavomat/scripts/APP.sh
```

## Startup and Management

### Starting Services

```bash
# Start doc_web
sudo /lavomat/scripts/DOC_WEB.sh start

# Start main Java app
sudo /lavomat/scripts/APP.sh start

# Start Nginx (if not already running)
sudo systemctl start nginx
```

### Stopping Services

```bash
# Stop doc_web
sudo /lavomat/scripts/DOC_WEB.sh stop

# Stop main Java app
sudo /lavomat/scripts/APP.sh stop

# Stop Nginx
sudo systemctl stop nginx
```

### Checking Status

```bash
# Check doc_web status
sudo /lavomat/scripts/DOC_WEB.sh status

# Check Java app status
sudo /lavomat/scripts/APP.sh status

# Check Nginx status
sudo systemctl status nginx

# Check all processes
ps aux | grep -E "(node|java|nginx)"
```

## Automated Documentation Updates

### Option A: Manual Update Script

```bash
# Create update script
sudo tee /lavomat/scripts/update_docs.sh << 'EOF'
#!/bin/bash

echo "Updating documentation..."

# Copy latest .apib files
cp -a /lavomat/git/lm_app/doc/*.apib /lavomat/doc_web/dist/

# Restart doc_web to pick up changes
/lavomat/scripts/DOC_WEB.sh restart

echo "Documentation updated successfully"
EOF

sudo chmod +x /lavomat/scripts/update_docs.sh
```

### Option B: Automated with File Watcher

```bash
# Install inotify-tools for file watching
sudo yum install -y inotify-tools

# Create watcher script
sudo tee /lavomat/scripts/doc_watcher.sh << 'EOF'
#!/bin/bash

WATCH_DIR="/lavomat/git/lm_app/doc"
TARGET_DIR="/lavomat/doc_web/dist"

echo "Starting documentation file watcher..."

inotifywait -m -r -e modify,create,delete --format '%w%f %e' "$WATCH_DIR" |
while read file event; do
    if [[ "$file" == *.apib ]]; then
        echo "Detected change in $file ($event)"
        cp -a "$WATCH_DIR"/*.apib "$TARGET_DIR"/
        echo "Documentation files updated"
    fi
done
EOF

sudo chmod +x /lavomat/scripts/doc_watcher.sh
```

## Monitoring and Logs

### Log Files

- **Doc Web**: `/var/log/lavomat/doc_web.log`
- **Java App**: `/var/log/lavomat/lmat.log`
- **Nginx Access**: `/var/log/nginx/access.log`
- **Nginx Error**: `/var/log/nginx/error.log`

### Monitoring Commands

```bash
# Monitor doc_web logs
tail -f /var/log/lavomat/doc_web.log

# Monitor Java app logs
tail -f /var/log/lavomat/lmat.log

# Monitor Nginx logs
tail -f /var/log/nginx/access.log

# Check port usage
netstat -tlnp | grep -E "(80|3000|9000)"

# Check memory usage
free -h
ps aux --sort=-%mem | head -10
```

## Troubleshooting

### Common Issues

1. **Port Conflicts**
   ```bash
   # Check what's using a port
   sudo lsof -i :3000
   sudo lsof -i :9000
   ```

2. **Permission Issues**
   ```bash
   # Fix ownership
   sudo chown -R lavomat:lavomat /lavomat/doc_web
   ```

3. **Node.js Module Issues**
   ```bash
   # Reinstall dependencies
   cd /lavomat/doc_web
   rm -rf node_modules
   npm install
   ```

4. **Nginx Configuration Issues**
   ```bash
   # Test configuration
   sudo nginx -t
   
   # Reload configuration
   sudo nginx -s reload
   ```

### Health Checks

```bash
# Test doc_web directly
curl http://localhost:3000

# Test through Nginx
curl http://localhost/docs

# Test Java app through Nginx
curl http://localhost/
```

## Security Considerations

1. **Firewall Rules**: Only expose port 80/443 externally
2. **SSL/TLS**: Configure SSL certificates for HTTPS
3. **Access Control**: Consider IP restrictions for documentation
4. **Regular Updates**: Keep Node.js, Nginx, and dependencies updated

## Performance Optimization

1. **Nginx Caching**: Enable caching for static documentation
2. **Gzip Compression**: Enable compression in Nginx
3. **Resource Limits**: Set appropriate memory limits for Node.js
4. **Log Rotation**: Configure log rotation to prevent disk space issues

## Backup and Recovery

1. **Documentation Files**: Include `/lavomat/doc_web/dist/` in backups
2. **Configuration Files**: Backup Nginx and application configs
3. **Database**: Ensure Java app database backups include doc references

This setup provides a robust, scalable solution for hosting both applications on the same EC2 instance with professional URL structure and easy maintenance.

## Quick Start

For a fully automated installation, run:

```bash
# Make the install script executable
chmod +x doc_web/scripts/install.sh

# Run the installation (as root)
sudo doc_web/scripts/install.sh
```

This will automatically:
- Install Node.js and Nginx
- Set up the doc_web application
- Configure reverse proxy
- Create service management scripts
- Start all services

## Manual Installation Steps

If you prefer manual installation or need to customize the setup:

1. **Install Node.js**: Follow Step 1 in the Implementation Guide
2. **Deploy Application**: Follow Step 2 in the Implementation Guide
3. **Configure Nginx**: Follow Step 3 in the Implementation Guide
4. **Set up Scripts**: Follow Step 4 in the Implementation Guide
5. **Update Java App**: Follow Step 5 in the Implementation Guide

## Service Management

Use the provided service script for easy management:

```bash
# Start the service
sudo /lavomat/scripts/DOC_WEB.sh start

# Stop the service
sudo /lavomat/scripts/DOC_WEB.sh stop

# Restart the service
sudo /lavomat/scripts/DOC_WEB.sh restart

# Check status
sudo /lavomat/scripts/DOC_WEB.sh status

# View logs
sudo /lavomat/scripts/DOC_WEB.sh logs

# Test the service
sudo /lavomat/scripts/DOC_WEB.sh test

# Update documentation files
sudo /lavomat/scripts/DOC_WEB.sh update-docs
```

## Documentation Updates

To update documentation files from the main repository:

```bash
# Update and restart service
sudo /lavomat/scripts/update_docs.sh

# Update without restarting
sudo /lavomat/scripts/update_docs.sh --no-restart

# Preview changes without applying
sudo /lavomat/scripts/update_docs.sh --dry-run

# Force update even if no changes detected
sudo /lavomat/scripts/update_docs.sh --force

# Create backup before updating
sudo /lavomat/scripts/update_docs.sh --backup
```

## Files Included

- `DEPLOYMENT_GUIDE.md` - This comprehensive guide
- `nginx.conf.example` - Nginx configuration template
- `scripts/install.sh` - Automated installation script
- `scripts/doc_web_service.sh` - Service management script
- `scripts/update_docs.sh` - Documentation update script

## Support

For issues or questions:
1. Check the logs: `/var/log/lavomat/doc_web.log`
2. Test the service: `sudo /lavomat/scripts/DOC_WEB.sh test`
3. Review the troubleshooting section above
4. Check Nginx configuration: `sudo nginx -t`
