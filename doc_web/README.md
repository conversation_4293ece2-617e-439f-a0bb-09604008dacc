# LAVOMAT Documentation Web Application

A Node.js/Express application that serves API documentation by rendering API Blueprint (.apib) files into HTML format.

## Overview

This application is designed to be hosted on the same EC2 instance as the main LAVOMAT Java+AngularJS application, providing a unified documentation portal accessible at `/docs`.

## Quick Start

### Local Development

1. Go to `./doc_web`
2. Create the `.env` file based on the `.env.example`
3. Run `nvm use` (or ensure Node.js 16+ is installed)
4. Run `yarn install` or `npm install`
5. Run `yarn start` or `npm start`
6. Go to http://localhost:3000/

### Production Deployment on EC2

For automated installation on the same EC2 instance as your Java+AngularJS app:

```bash
# Make the install script executable
chmod +x scripts/install.sh

# Run the installation (requires root)
sudo scripts/install.sh
```

This will:
- Install Node.js and Nginx
- Set up reverse proxy routing
- Configure the doc_web service
- Start all services

After installation:
- Main app: `http://your-domain.com`
- Documentation: `http://your-domain.com/docs`

## Features

- **API Blueprint Rendering**: Converts .apib files to beautiful HTML documentation
- **Express Server**: Lightweight Node.js web server
- **EJS Templates**: Customizable documentation layout
- **Aglio Integration**: Uses Aglio for API Blueprint processing
- **Auto-reload**: Automatically picks up documentation changes
- **Reverse Proxy Ready**: Designed to work with Nginx reverse proxy

## Service Management (Production)

After installation, use the service management script:

```bash
# Service operations
sudo /lavomat/scripts/DOC_WEB.sh {start|stop|restart|status}

# Monitoring
sudo /lavomat/scripts/DOC_WEB.sh logs
sudo /lavomat/scripts/DOC_WEB.sh test

# Documentation updates
sudo /lavomat/scripts/update_docs.sh
```

## Documentation

- **[DEPLOYMENT_GUIDE.md](DEPLOYMENT_GUIDE.md)** - Comprehensive deployment guide for EC2
- **[nginx.conf.example](nginx.conf.example)** - Nginx configuration template
- **[scripts/](scripts/)** - Installation and management scripts

## File Structure

```
doc_web/
├── index.js              # Main application file
├── utils.js              # Utility functions
├── package.json          # Dependencies
├── dist/                 # Documentation files (.apib)
├── views/                # EJS templates
├── scripts/              # Deployment scripts
├── nginx.conf.example    # Nginx config
└── DEPLOYMENT_GUIDE.md   # Detailed guide
```

## Configuration

### Environment Variables

- `PORT`: Server port (default: 3000)
- `NODE_ENV`: Environment (development/production)

### Deployment Options

1. **Reverse Proxy (Recommended)**: `/docs` path on main domain
2. **Different Ports**: Separate port access
3. **Subdomain**: `docs.your-domain.com`

## Troubleshooting

### Common Issues

1. **Port conflicts**: Check with `sudo lsof -i :3000`
2. **Permission issues**: Fix with `sudo chown -R lavomat:lavomat /lavomat/doc_web`
3. **Missing docs**: Update with `sudo /lavomat/scripts/update_docs.sh`

### Debug

```bash
# Check logs
tail -f /var/log/lavomat/doc_web.log

# Test service
sudo /lavomat/scripts/DOC_WEB.sh test

# Check Nginx config
sudo nginx -t
```

## References

* [aglio](https://github.com/danielgtaylor/aglio) - API Blueprint renderer
* [Express.js](https://expressjs.com/) - Web framework
* [EJS](https://ejs.co/) - Template engine
