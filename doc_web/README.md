# LAVOMAT Documentation Web Application

A Node.js/Express application that serves API documentation by rendering API Blueprint (.apib) files into HTML format.

## Overview

This application is designed to be hosted on the same EC2 instance as the main LAVOMAT Java+AngularJS application, providing a unified documentation portal accessible at `/docs`.

## Deployment Options

You have **two options** for hosting documentation with your Java+AngularJS app:

### Option 1: Java Integration (Recommended)

Integrate documentation directly into your Play Framework application - **no separate services needed!**

**Advantages:**
- ✅ Single application to deploy and maintain
- ✅ No Node.js, Nginx, or additional services required
- ✅ Better integration with existing authentication/authorization
- ✅ Same technology stack (Java/Scala)
- ✅ Enhanced features (JSON API, better search)

**Quick Start:**
```bash
# Copy the Java files to your Play app
cp app/controllers/DocumentationController.java /your-app/app/controllers/
cp app/services/ApiDocumentationService.java /your-app/app/services/
cp app/views/documentation*.scala.html /your-app/app/views/

# Add routes to conf/routes (see JAVA_INTEGRATION_GUIDE.md)
# Compile and run your Play app
./activator run

# Access at http://localhost:9000/docs
```

📖 **[See JAVA_INTEGRATION_GUIDE.md for complete instructions](JAVA_INTEGRATION_GUIDE.md)**

### Option 2: Separate Node.js Service

Run doc_web as a separate Node.js application alongside your Java app.

**Use this if:**
- You prefer to keep services separate
- You want to use the exact same technology as the original doc_web
- You need specific Node.js features

#### Local Development
1. Go to `./doc_web`
2. Create the `.env` file based on the `.env.example`
3. Run `nvm use` (or ensure Node.js 16+ is installed)
4. Run `yarn install` or `npm install`
5. Run `yarn start` or `npm start`
6. Go to http://localhost:3000/

#### Production Deployment on EC2
```bash
# Automated installation
chmod +x scripts/install.sh
sudo scripts/install.sh
```

This sets up:
- Node.js and Nginx
- Reverse proxy routing
- Service management scripts

📖 **[See DEPLOYMENT_GUIDE.md for complete instructions](DEPLOYMENT_GUIDE.md)**

## Features

- **API Blueprint Rendering**: Converts .apib files to beautiful HTML documentation
- **Express Server**: Lightweight Node.js web server
- **EJS Templates**: Customizable documentation layout
- **Aglio Integration**: Uses Aglio for API Blueprint processing
- **Auto-reload**: Automatically picks up documentation changes
- **Reverse Proxy Ready**: Designed to work with Nginx reverse proxy

## Service Management (Production)

After installation, use the service management script:

```bash
# Service operations
sudo /lavomat/scripts/DOC_WEB.sh {start|stop|restart|status}

# Monitoring
sudo /lavomat/scripts/DOC_WEB.sh logs
sudo /lavomat/scripts/DOC_WEB.sh test

# Documentation updates
sudo /lavomat/scripts/update_docs.sh
```

## Documentation

- **[DEPLOYMENT_GUIDE.md](DEPLOYMENT_GUIDE.md)** - Comprehensive deployment guide for EC2
- **[nginx.conf.example](nginx.conf.example)** - Nginx configuration template
- **[scripts/](scripts/)** - Installation and management scripts

## File Structure

```
doc_web/
├── index.js              # Main application file
├── utils.js              # Utility functions
├── package.json          # Dependencies
├── dist/                 # Documentation files (.apib)
├── views/                # EJS templates
├── scripts/              # Deployment scripts
├── nginx.conf.example    # Nginx config
└── DEPLOYMENT_GUIDE.md   # Detailed guide
```

## Configuration

### Environment Variables

- `PORT`: Server port (default: 3000)
- `NODE_ENV`: Environment (development/production)

### Deployment Options

1. **Reverse Proxy (Recommended)**: `/docs` path on main domain
2. **Different Ports**: Separate port access
3. **Subdomain**: `docs.your-domain.com`

## Troubleshooting

### Common Issues

1. **Port conflicts**: Check with `sudo lsof -i :3000`
2. **Permission issues**: Fix with `sudo chown -R lavomat:lavomat /lavomat/doc_web`
3. **Missing docs**: Update with `sudo /lavomat/scripts/update_docs.sh`

### Debug

```bash
# Check logs
tail -f /var/log/lavomat/doc_web.log

# Test service
sudo /lavomat/scripts/DOC_WEB.sh test

# Check Nginx config
sudo nginx -t
```

## References

* [aglio](https://github.com/danielgtaylor/aglio) - API Blueprint renderer
* [Express.js](https://expressjs.com/) - Web framework
* [EJS](https://ejs.co/) - Template engine
