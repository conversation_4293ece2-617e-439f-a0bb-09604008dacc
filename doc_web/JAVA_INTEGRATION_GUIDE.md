# Java Integration Guide - Documentation in Play Framework

This guide shows how to integrate the doc_web functionality directly into your Java Play Framework application instead of running it as a separate Node.js service.

## Overview

Instead of hosting a separate Node.js application, we've created a Java-based solution that:

- **Integrates seamlessly** with your existing Play Framework application
- **Renders API Blueprint files** to beautiful HTML documentation
- **Provides the same functionality** as the Node.js doc_web app
- **Uses familiar Java/Scala technologies** you already have
- **Eliminates the need** for Node.js, Nginx reverse proxy, or additional services

## Architecture

### Components Created

1. **`DocumentationController.java`** - Main controller handling documentation routes
2. **`ApiDocumentationService.java`** - Service for parsing and rendering API Blueprint files
3. **`documentation_index.scala.html`** - Template for the documentation index page
4. **`documentation_view.scala.html`** - Template for individual documentation pages
5. **Route definitions** in `conf/routes`

### URL Structure

- **`/docs`** - Documentation index page (lists all available docs)
- **`/docs/{docName}`** - Rendered HTML documentation for specific file
- **`/docs/{docName}/raw`** - Raw content of documentation file
- **`/api/v1/docs`** - JSON API listing all documentation
- **`/api/v1/docs/{docName}`** - JSON metadata for specific documentation

## Implementation Details

### DocumentationController Features

<augment_code_snippet path="app/controllers/DocumentationController.java" mode="EXCERPT">
````java
/**
 * Documentation Controller
 * 
 * Provides API documentation functionality similar to the doc_web Node.js application
 * but integrated directly into the Play Framework application.
 */
public class DocumentationController extends Controller {
    
    @Inject
    private ApiDocumentationService documentationService;
    
    /**
     * GET /docs - Renders the documentation index page
     */
    public Result index() {
        List<DocumentInfo> docs = getAvailableDocuments();
        return ok(documentation_index.render("LAVOMAT - API Documentation", docs));
    }
    
    /**
     * GET /docs/{docName} - Renders specific documentation
     */
    public Result viewDocument(String docName) {
        // Renders API Blueprint to HTML or shows raw content
    }
````
</augment_code_snippet>

### ApiDocumentationService Features

<augment_code_snippet path="app/services/ApiDocumentationService.java" mode="EXCERPT">
````java
/**
 * API Documentation Service
 * 
 * Provides functionality to parse and render API Blueprint (.apib) files
 * into HTML format, similar to what Aglio does in the Node.js ecosystem.
 */
public class ApiDocumentationService {
    
    /**
     * Renders an API Blueprint file to HTML
     */
    public String renderToHtml(String apibContent, String title) {
        ApiBlueprint blueprint = parseApiBlueprint(apibContent);
        return generateHtml(blueprint, title);
    }
````
</augment_code_snippet>

## Installation Steps

### 1. Copy the Java Files

Copy these files to your Play Framework application:

```bash
# Controllers
cp app/controllers/DocumentationController.java /path/to/your/app/controllers/

# Services  
cp app/services/ApiDocumentationService.java /path/to/your/app/services/

# Views
cp app/views/documentation_index.scala.html /path/to/your/app/views/
cp app/views/documentation_view.scala.html /path/to/your/app/views/
```

### 2. Update Routes

Add these routes to your `conf/routes` file:

```
# Documentation
GET     /docs                                                   controllers.DocumentationController.index()
GET     /docs/:docName                                          controllers.DocumentationController.viewDocument(docName:String)
GET     /docs/:docName/raw                                      controllers.DocumentationController.getRawDocument(docName:String)
GET     /api/v1/docs                                            controllers.DocumentationController.listDocumentsApi()
GET     /api/v1/docs/:docName                                   controllers.DocumentationController.getDocumentApi(docName:String)
```

### 3. Ensure Documentation Directory

Make sure your `doc/` directory contains `.apib` files:

```bash
# Your project structure should look like:
your-app/
├── app/
│   ├── controllers/
│   │   └── DocumentationController.java
│   ├── services/
│   │   └── ApiDocumentationService.java
│   └── views/
│       ├── documentation_index.scala.html
│       └── documentation_view.scala.html
├── conf/
│   └── routes
└── doc/
    ├── api_v1_buildings.apib
    ├── tasks_v1.apib
    └── other_docs.apib
```

### 4. Compile and Run

```bash
# Compile the application
./activator compile

# Run the application
./activator run

# Access documentation at:
# http://localhost:9000/docs
```

## Features Comparison

| Feature | Node.js doc_web | Java Integration |
|---------|----------------|------------------|
| **API Blueprint Rendering** | ✅ (via Aglio) | ✅ (Custom parser) |
| **Documentation Index** | ✅ | ✅ |
| **Search Functionality** | ✅ | ✅ |
| **Raw File Access** | ✅ | ✅ |
| **JSON API** | ❌ | ✅ |
| **Responsive Design** | ✅ | ✅ |
| **No Additional Services** | ❌ | ✅ |
| **Same Technology Stack** | ❌ | ✅ |

## Advantages of Java Integration

### 1. **Simplified Architecture**
- No need for Node.js installation
- No need for Nginx reverse proxy
- No need for additional service management
- Single application to deploy and maintain

### 2. **Better Integration**
- Uses same authentication/authorization as main app
- Shares same logging and monitoring
- Can access application database and services
- Consistent error handling and configuration

### 3. **Enhanced Features**
- JSON API for documentation metadata
- Better search and filtering capabilities
- Integration with existing user management
- Consistent styling with main application

### 4. **Easier Deployment**
- Single JAR/WAR file deployment
- No additional infrastructure requirements
- Simplified CI/CD pipeline
- Consistent environment configuration

## API Blueprint Parsing

The `ApiDocumentationService` provides a simplified API Blueprint parser that handles:

- **Document titles and hosts**
- **Endpoint definitions** (method, path, title)
- **Parameter documentation**
- **Response codes and content types**
- **Descriptions and metadata**

### Example API Blueprint Support

```apib
FORMAT: 1A
HOST: https://api.example.com

# My API

## Users [/users]

### List Users [GET /users]

- Purpose: Get all users

+ Response 200 (application/json)
```

This gets rendered to beautiful HTML with:
- Syntax highlighting
- Organized sections
- Navigation menu
- Responsive design

## Customization

### Styling

Modify the CSS in `ApiDocumentationService.getDocumentationStyles()` to match your application's design:

```java
private String getDocumentationStyles() {
    return """
        <style>
            /* Customize colors, fonts, layout */
            .header { background: your-brand-color; }
            .method-get { background: your-success-color; }
            /* ... */
        </style>
    """;
}
```

### Templates

Customize the Scala HTML templates:
- `documentation_index.scala.html` - Main documentation listing
- `documentation_view.scala.html` - Individual document view

### Parser Enhancement

Extend `ApiDocumentationService` to support:
- More API Blueprint features
- Different documentation formats
- Custom rendering options
- Integration with external tools

## Security Considerations

### Access Control

Add authentication/authorization to the controller:

```java
@Security.Authenticated(Secured.class)
public Result index() {
    // Only authenticated users can access docs
}
```

### Input Validation

The controller includes:
- Path traversal protection
- Input sanitization
- Error handling
- Safe file access

## Performance

### Caching

Consider adding caching for rendered documentation:

```java
@Cached(key = "doc.{docName}", duration = 3600)
public Result viewDocument(String docName) {
    // Cached for 1 hour
}
```

### Optimization

- Documentation is rendered on-demand
- File system access is optimized
- HTML generation is efficient
- Responsive design reduces bandwidth

## Migration from Node.js

If you're currently using the Node.js doc_web:

### 1. **Keep Both Temporarily**
- Deploy Java version alongside Node.js
- Test thoroughly before switching
- Compare rendered output

### 2. **Update Links**
- Change documentation links to `/docs`
- Update any automation that references doc URLs
- Notify users of the new location

### 3. **Remove Node.js Service**
- Stop the Node.js service
- Remove Nginx reverse proxy configuration
- Clean up Node.js installation and files

## Troubleshooting

### Common Issues

1. **Documentation not found**
   ```bash
   # Check if doc/ directory exists and contains .apib files
   ls -la doc/
   ```

2. **Compilation errors**
   ```bash
   # Ensure all imports are correct
   ./activator compile
   ```

3. **Template not found**
   ```bash
   # Verify template files are in app/views/
   ls -la app/views/documentation*
   ```

### Debug Mode

Enable debug logging in `application.conf`:

```
logger.controllers.DocumentationController=DEBUG
logger.services.ApiDocumentationService=DEBUG
```

## Future Enhancements

### Possible Improvements

1. **Full API Blueprint Support**
   - Integrate with existing Java API Blueprint libraries
   - Support for more complex API Blueprint features
   - Better parsing of request/response examples

2. **Interactive Documentation**
   - API testing interface
   - Request/response examples
   - Authentication integration

3. **Documentation Management**
   - Web-based editing interface
   - Version control integration
   - Automated documentation generation

4. **Advanced Features**
   - PDF export
   - Offline documentation
   - Multi-language support

This Java integration provides a robust, maintainable solution that eliminates the complexity of running multiple services while providing the same functionality as the Node.js doc_web application.
