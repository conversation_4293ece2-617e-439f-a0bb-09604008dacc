#!/bin/bash

# Doc Web Service Management Script
# Place this file in /lavomat/scripts/DOC_WEB.sh
# Make executable: chmod +x /lavomat/scripts/DOC_WEB.sh

# Configuration
APP_NAME="doc_web"
APP_DIR="/lavomat/doc_web"
APP_USER="lavomat"
PID_FILE="/var/run/doc_web.pid"
LOG_FILE="/var/log/lavomat/doc_web.log"
NODE_ENV="production"
PORT="3000"

# Ensure log directory exists
mkdir -p "$(dirname "$LOG_FILE")"

# Function to get the PID of the running process
get_pid() {
    if [ -f "$PID_FILE" ]; then
        cat "$PID_FILE"
    fi
}

# Function to check if the process is running
is_running() {
    local pid=$(get_pid)
    if [ -n "$pid" ] && ps -p "$pid" > /dev/null 2>&1; then
        return 0
    else
        return 1
    fi
}

# Function to start the application
start() {
    if is_running; then
        echo "$APP_NAME is already running (PID: $(get_pid))"
        return 1
    fi

    echo "Starting $APP_NAME..."
    
    # Change to application directory
    cd "$APP_DIR" || {
        echo "Error: Cannot change to directory $APP_DIR"
        return 1
    }

    # Check if package.json exists
    if [ ! -f "package.json" ]; then
        echo "Error: package.json not found in $APP_DIR"
        return 1
    fi

    # Check if node_modules exists
    if [ ! -d "node_modules" ]; then
        echo "Installing dependencies..."
        npm install
    fi

    # Ensure dist directory exists and copy documentation files
    mkdir -p dist
    if [ -d "/lavomat/git/lm_app/doc" ]; then
        cp -a /lavomat/git/lm_app/doc/*.apib ./dist/ 2>/dev/null || true
    fi

    # Set environment variables
    export NODE_ENV="$NODE_ENV"
    export PORT="$PORT"

    # Start the application
    if [ "$APP_USER" = "$(whoami)" ]; then
        # Running as the correct user
        nohup node index.js >> "$LOG_FILE" 2>&1 &
        local pid=$!
    else
        # Need to switch user
        sudo -u "$APP_USER" bash -c "
            cd '$APP_DIR'
            export NODE_ENV='$NODE_ENV'
            export PORT='$PORT'
            nohup node index.js >> '$LOG_FILE' 2>&1 &
            echo \$!
        " > /tmp/doc_web_pid
        local pid=$(cat /tmp/doc_web_pid)
        rm -f /tmp/doc_web_pid
    fi

    # Save PID
    echo "$pid" > "$PID_FILE"

    # Wait a moment and check if it started successfully
    sleep 2
    if is_running; then
        echo "$APP_NAME started successfully (PID: $pid)"
        return 0
    else
        echo "Failed to start $APP_NAME"
        rm -f "$PID_FILE"
        return 1
    fi
}

# Function to stop the application
stop() {
    if ! is_running; then
        echo "$APP_NAME is not running"
        rm -f "$PID_FILE"
        return 0
    fi

    local pid=$(get_pid)
    echo "Stopping $APP_NAME (PID: $pid)..."

    # Try graceful shutdown first
    kill "$pid" 2>/dev/null

    # Wait for graceful shutdown
    local count=0
    while [ $count -lt 10 ] && is_running; do
        sleep 1
        count=$((count + 1))
    done

    # Force kill if still running
    if is_running; then
        echo "Forcing shutdown..."
        kill -9 "$pid" 2>/dev/null
        sleep 1
    fi

    # Clean up PID file
    rm -f "$PID_FILE"

    if is_running; then
        echo "Failed to stop $APP_NAME"
        return 1
    else
        echo "$APP_NAME stopped successfully"
        return 0
    fi
}

# Function to restart the application
restart() {
    echo "Restarting $APP_NAME..."
    stop
    sleep 2
    start
}

# Function to show status
status() {
    if is_running; then
        local pid=$(get_pid)
        echo "$APP_NAME is running (PID: $pid)"
        
        # Show additional info
        echo "Port: $PORT"
        echo "Log file: $LOG_FILE"
        echo "Working directory: $APP_DIR"
        
        # Show memory usage
        local mem_usage=$(ps -o pid,ppid,cmd,%mem,%cpu --sort=-%mem -p "$pid" 2>/dev/null | tail -n +2)
        if [ -n "$mem_usage" ]; then
            echo "Memory usage:"
            echo "PID   PPID CMD                         %MEM %CPU"
            echo "$mem_usage"
        fi
        
        return 0
    else
        echo "$APP_NAME is not running"
        if [ -f "$PID_FILE" ]; then
            echo "Stale PID file found: $PID_FILE"
        fi
        return 1
    fi
}

# Function to show logs
logs() {
    local lines=${1:-50}
    if [ -f "$LOG_FILE" ]; then
        echo "Last $lines lines of $LOG_FILE:"
        tail -n "$lines" "$LOG_FILE"
    else
        echo "Log file not found: $LOG_FILE"
    fi
}

# Function to follow logs
follow_logs() {
    if [ -f "$LOG_FILE" ]; then
        echo "Following $LOG_FILE (Ctrl+C to stop):"
        tail -f "$LOG_FILE"
    else
        echo "Log file not found: $LOG_FILE"
    fi
}

# Function to update documentation files
update_docs() {
    echo "Updating documentation files..."
    
    if [ ! -d "$APP_DIR/dist" ]; then
        mkdir -p "$APP_DIR/dist"
    fi
    
    if [ -d "/lavomat/git/lm_app/doc" ]; then
        cp -a /lavomat/git/lm_app/doc/*.apib "$APP_DIR/dist/" 2>/dev/null
        echo "Documentation files updated from /lavomat/git/lm_app/doc/"
        
        # Restart if running to pick up changes
        if is_running; then
            echo "Restarting $APP_NAME to pick up changes..."
            restart
        fi
    else
        echo "Warning: Source documentation directory not found: /lavomat/git/lm_app/doc"
    fi
}

# Function to test the application
test() {
    echo "Testing $APP_NAME..."
    
    # Test if port is listening
    if netstat -tlnp 2>/dev/null | grep -q ":$PORT "; then
        echo "✓ Port $PORT is listening"
    else
        echo "✗ Port $PORT is not listening"
        return 1
    fi
    
    # Test HTTP response
    local response=$(curl -s -o /dev/null -w "%{http_code}" "http://localhost:$PORT" 2>/dev/null)
    if [ "$response" = "200" ]; then
        echo "✓ HTTP response: $response"
    else
        echo "✗ HTTP response: $response (expected 200)"
        return 1
    fi
    
    echo "$APP_NAME is responding correctly"
    return 0
}

# Function to show help
show_help() {
    echo "Usage: $0 {start|stop|restart|status|logs|follow-logs|update-docs|test|help}"
    echo ""
    echo "Commands:"
    echo "  start        Start the $APP_NAME service"
    echo "  stop         Stop the $APP_NAME service"
    echo "  restart      Restart the $APP_NAME service"
    echo "  status       Show service status and resource usage"
    echo "  logs [N]     Show last N lines of logs (default: 50)"
    echo "  follow-logs  Follow logs in real-time"
    echo "  update-docs  Update documentation files from source"
    echo "  test         Test if the service is responding correctly"
    echo "  help         Show this help message"
    echo ""
    echo "Configuration:"
    echo "  App Directory: $APP_DIR"
    echo "  PID File: $PID_FILE"
    echo "  Log File: $LOG_FILE"
    echo "  Port: $PORT"
    echo "  User: $APP_USER"
}

# Main script logic
case "${1:-}" in
    start)
        start
        ;;
    stop)
        stop
        ;;
    restart)
        restart
        ;;
    status)
        status
        ;;
    logs)
        logs "${2:-50}"
        ;;
    follow-logs)
        follow_logs
        ;;
    update-docs)
        update_docs
        ;;
    test)
        test
        ;;
    help|--help|-h)
        show_help
        ;;
    *)
        echo "Error: Unknown command '${1:-}'"
        echo ""
        show_help
        exit 1
        ;;
esac

exit $?
