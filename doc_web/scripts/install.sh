#!/bin/bash

# Doc Web Installation Script
# This script automates the installation and setup of the doc_web application
# on the same EC2 instance as the Java+AngularJS lm-app

set -e  # Exit on any error

# Configuration
INSTALL_DIR="/lavomat/doc_web"
SCRIPTS_DIR="/lavomat/scripts"
LOG_DIR="/var/log/lavomat"
APP_USER="lavomat"
NODE_VERSION="16"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Logging functions
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Function to check if running as root
check_root() {
    if [ "$EUID" -ne 0 ]; then
        log_error "This script must be run as root (use sudo)"
        exit 1
    fi
}

# Function to check if user exists
check_user() {
    if ! id "$APP_USER" &>/dev/null; then
        log_error "User '$APP_USER' does not exist. Please create it first."
        exit 1
    fi
}

# Function to install Node.js
install_nodejs() {
    log_info "Checking Node.js installation..."
    
    if command -v node &> /dev/null; then
        local current_version=$(node --version | cut -d'v' -f2 | cut -d'.' -f1)
        if [ "$current_version" -ge "$NODE_VERSION" ]; then
            log_success "Node.js $current_version is already installed"
            return 0
        else
            log_warning "Node.js $current_version is installed but version $NODE_VERSION or higher is required"
        fi
    fi
    
    log_info "Installing Node.js $NODE_VERSION..."
    
    # Install Node.js repository
    curl -fsSL https://rpm.nodesource.com/setup_${NODE_VERSION}.x | bash -
    
    # Install Node.js
    yum install -y nodejs
    
    # Verify installation
    if command -v node &> /dev/null && command -v npm &> /dev/null; then
        log_success "Node.js $(node --version) and npm $(npm --version) installed successfully"
    else
        log_error "Failed to install Node.js"
        exit 1
    fi
}

# Function to install Nginx
install_nginx() {
    log_info "Checking Nginx installation..."
    
    if command -v nginx &> /dev/null; then
        log_success "Nginx is already installed"
        return 0
    fi
    
    log_info "Installing Nginx..."
    
    # Install Nginx
    yum install -y nginx
    
    # Enable Nginx service
    systemctl enable nginx
    
    log_success "Nginx installed and enabled"
}

# Function to create directories
create_directories() {
    log_info "Creating directories..."
    
    # Create main directories
    mkdir -p "$INSTALL_DIR"
    mkdir -p "$SCRIPTS_DIR"
    mkdir -p "$LOG_DIR"
    
    # Set ownership
    chown -R "$APP_USER:$APP_USER" "$INSTALL_DIR"
    chown -R "$APP_USER:$APP_USER" "$LOG_DIR"
    
    log_success "Directories created and ownership set"
}

# Function to copy application files
copy_application() {
    log_info "Copying application files..."
    
    # Get the directory where this script is located
    local script_dir="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
    local source_dir="$(dirname "$script_dir")"
    
    # Copy application files
    cp -r "$source_dir"/* "$INSTALL_DIR/"
    
    # Remove scripts directory from app directory (it's copied to /lavomat/scripts)
    rm -rf "$INSTALL_DIR/scripts"
    
    # Set ownership
    chown -R "$APP_USER:$APP_USER" "$INSTALL_DIR"
    
    log_success "Application files copied"
}

# Function to install dependencies
install_dependencies() {
    log_info "Installing Node.js dependencies..."
    
    cd "$INSTALL_DIR"
    
    # Install dependencies as the app user
    sudo -u "$APP_USER" npm install --production
    
    log_success "Dependencies installed"
}

# Function to setup service script
setup_service_script() {
    log_info "Setting up service script..."
    
    # Copy service script
    cp "$INSTALL_DIR/scripts/doc_web_service.sh" "$SCRIPTS_DIR/DOC_WEB.sh"
    
    # Make executable
    chmod +x "$SCRIPTS_DIR/DOC_WEB.sh"
    
    log_success "Service script installed at $SCRIPTS_DIR/DOC_WEB.sh"
}

# Function to setup Nginx configuration
setup_nginx() {
    log_info "Setting up Nginx configuration..."
    
    # Backup existing configuration if it exists
    if [ -f "/etc/nginx/conf.d/lavomat.conf" ]; then
        cp "/etc/nginx/conf.d/lavomat.conf" "/etc/nginx/conf.d/lavomat.conf.backup.$(date +%Y%m%d_%H%M%S)"
        log_warning "Existing configuration backed up"
    fi
    
    # Copy Nginx configuration
    cp "$INSTALL_DIR/nginx.conf.example" "/etc/nginx/conf.d/lavomat.conf"
    
    # Update server_name in configuration (you'll need to manually edit this)
    log_warning "Please edit /etc/nginx/conf.d/lavomat.conf and update 'your-domain.com' with your actual domain"
    
    # Test Nginx configuration
    if nginx -t; then
        log_success "Nginx configuration is valid"
    else
        log_error "Nginx configuration has errors. Please check and fix."
        exit 1
    fi
}

# Function to setup environment
setup_environment() {
    log_info "Setting up environment..."
    
    cd "$INSTALL_DIR"
    
    # Create .env file if it doesn't exist
    if [ ! -f ".env" ]; then
        cp ".env.example" ".env"
        echo "PORT=3000" > ".env"
        chown "$APP_USER:$APP_USER" ".env"
        log_success "Environment file created"
    else
        log_warning "Environment file already exists"
    fi
    
    # Create dist directory and copy documentation files
    mkdir -p dist
    if [ -d "/lavomat/git/lm_app/doc" ]; then
        cp -a /lavomat/git/lm_app/doc/*.apib ./dist/ 2>/dev/null || true
        log_success "Documentation files copied"
    else
        log_warning "Source documentation directory not found: /lavomat/git/lm_app/doc"
    fi
    
    chown -R "$APP_USER:$APP_USER" dist
}

# Function to update Java app configuration
update_java_app() {
    log_info "Updating Java application configuration..."
    
    local app_script="$SCRIPTS_DIR/APP.sh"
    
    if [ -f "$app_script" ]; then
        # Backup original script
        cp "$app_script" "$app_script.backup.$(date +%Y%m%d_%H%M%S)"
        
        # Update to use port 9000 instead of default
        if grep -q "Dhttp.port=9000" "$app_script"; then
            log_success "Java app already configured for port 9000"
        else
            # Add port configuration to the Java startup command
            sed -i 's/bin\/lm_app/bin\/lm_app -Dhttp.port=9000/g' "$app_script"
            log_success "Java app configured to use port 9000"
        fi
    else
        log_warning "Java app script not found at $app_script"
    fi
}

# Function to create systemd service (optional)
create_systemd_service() {
    log_info "Creating systemd service..."
    
    cat > /etc/systemd/system/doc-web.service << EOF
[Unit]
Description=LAVOMAT Documentation Web Service
After=network.target

[Service]
Type=forking
User=$APP_USER
WorkingDirectory=$INSTALL_DIR
Environment=NODE_ENV=production
Environment=PORT=3000
ExecStart=$SCRIPTS_DIR/DOC_WEB.sh start
ExecStop=$SCRIPTS_DIR/DOC_WEB.sh stop
ExecReload=$SCRIPTS_DIR/DOC_WEB.sh restart
PIDFile=/var/run/doc_web.pid
Restart=always
RestartSec=10

[Install]
WantedBy=multi-user.target
EOF

    # Reload systemd and enable service
    systemctl daemon-reload
    systemctl enable doc-web.service
    
    log_success "Systemd service created and enabled"
}

# Function to start services
start_services() {
    log_info "Starting services..."
    
    # Start doc_web
    "$SCRIPTS_DIR/DOC_WEB.sh" start
    
    # Start Nginx
    systemctl start nginx
    
    # Wait a moment for services to start
    sleep 3
    
    # Test services
    if "$SCRIPTS_DIR/DOC_WEB.sh" test; then
        log_success "Doc Web service is running and responding"
    else
        log_error "Doc Web service failed to start properly"
    fi
    
    if systemctl is-active --quiet nginx; then
        log_success "Nginx is running"
    else
        log_error "Nginx failed to start"
    fi
}

# Function to show final instructions
show_final_instructions() {
    log_success "Installation completed successfully!"
    echo ""
    echo "Next steps:"
    echo "1. Edit /etc/nginx/conf.d/lavomat.conf and replace 'your-domain.com' with your actual domain"
    echo "2. Restart Nginx: sudo systemctl restart nginx"
    echo "3. Update your Java app configuration if needed"
    echo "4. Test the setup:"
    echo "   - Main app: http://your-domain.com"
    echo "   - Documentation: http://your-domain.com/docs"
    echo ""
    echo "Service management commands:"
    echo "  sudo $SCRIPTS_DIR/DOC_WEB.sh {start|stop|restart|status|logs|test}"
    echo ""
    echo "Log files:"
    echo "  Doc Web: $LOG_DIR/doc_web.log"
    echo "  Nginx: /var/log/nginx/lavomat_access.log"
    echo ""
}

# Main installation process
main() {
    log_info "Starting LAVOMAT Doc Web installation..."
    
    check_root
    check_user
    install_nodejs
    install_nginx
    create_directories
    copy_application
    install_dependencies
    setup_service_script
    setup_nginx
    setup_environment
    update_java_app
    create_systemd_service
    start_services
    show_final_instructions
}

# Run main function
main "$@"
