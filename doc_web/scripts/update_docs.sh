#!/bin/bash

# Documentation Update Script
# This script updates the documentation files from the main lm-app repository
# and optionally restarts the doc_web service to pick up changes

set -e  # Exit on any error

# Configuration
DOC_WEB_DIR="/lavomat/doc_web"
SOURCE_DOC_DIR="/lavomat/git/lm_app/doc"
SCRIPTS_DIR="/lavomat/scripts"
APP_USER="lavomat"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Logging functions
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Function to show help
show_help() {
    echo "Usage: $0 [OPTIONS]"
    echo ""
    echo "Update documentation files for the doc_web application"
    echo ""
    echo "Options:"
    echo "  --no-restart    Don't restart the doc_web service after update"
    echo "  --dry-run       Show what would be done without making changes"
    echo "  --force         Force update even if no changes detected"
    echo "  --backup        Create backup before updating"
    echo "  --help, -h      Show this help message"
    echo ""
    echo "Examples:"
    echo "  $0                    # Update docs and restart service"
    echo "  $0 --no-restart      # Update docs without restarting"
    echo "  $0 --dry-run         # Preview changes"
    echo "  $0 --backup          # Create backup before update"
}

# Parse command line arguments
RESTART_SERVICE=true
DRY_RUN=false
FORCE_UPDATE=false
CREATE_BACKUP=false

while [[ $# -gt 0 ]]; do
    case $1 in
        --no-restart)
            RESTART_SERVICE=false
            shift
            ;;
        --dry-run)
            DRY_RUN=true
            shift
            ;;
        --force)
            FORCE_UPDATE=true
            shift
            ;;
        --backup)
            CREATE_BACKUP=true
            shift
            ;;
        --help|-h)
            show_help
            exit 0
            ;;
        *)
            log_error "Unknown option: $1"
            show_help
            exit 1
            ;;
    esac
done

# Function to check if directories exist
check_directories() {
    if [ ! -d "$SOURCE_DOC_DIR" ]; then
        log_error "Source documentation directory not found: $SOURCE_DOC_DIR"
        log_info "Please ensure the lm-app repository is cloned and up to date"
        exit 1
    fi

    if [ ! -d "$DOC_WEB_DIR" ]; then
        log_error "Doc web directory not found: $DOC_WEB_DIR"
        log_info "Please ensure doc_web is properly installed"
        exit 1
    fi

    if [ ! -d "$DOC_WEB_DIR/dist" ]; then
        log_info "Creating dist directory: $DOC_WEB_DIR/dist"
        if [ "$DRY_RUN" = false ]; then
            mkdir -p "$DOC_WEB_DIR/dist"
            chown "$APP_USER:$APP_USER" "$DOC_WEB_DIR/dist"
        fi
    fi
}

# Function to create backup
create_backup() {
    if [ "$CREATE_BACKUP" = true ] && [ -d "$DOC_WEB_DIR/dist" ]; then
        local backup_dir="$DOC_WEB_DIR/dist.backup.$(date +%Y%m%d_%H%M%S)"
        log_info "Creating backup: $backup_dir"
        
        if [ "$DRY_RUN" = false ]; then
            cp -r "$DOC_WEB_DIR/dist" "$backup_dir"
            chown -R "$APP_USER:$APP_USER" "$backup_dir"
            log_success "Backup created: $backup_dir"
        else
            log_info "[DRY RUN] Would create backup: $backup_dir"
        fi
    fi
}

# Function to check for changes
check_changes() {
    local changes_detected=false
    
    log_info "Checking for documentation changes..."
    
    # Get list of .apib files in source
    local source_files=($(find "$SOURCE_DOC_DIR" -name "*.apib" -type f))
    
    if [ ${#source_files[@]} -eq 0 ]; then
        log_warning "No .apib files found in source directory"
        return 1
    fi
    
    log_info "Found ${#source_files[@]} .apib files in source"
    
    # Check each source file
    for source_file in "${source_files[@]}"; do
        local filename=$(basename "$source_file")
        local dest_file="$DOC_WEB_DIR/dist/$filename"
        
        if [ ! -f "$dest_file" ]; then
            log_info "New file detected: $filename"
            changes_detected=true
        elif ! cmp -s "$source_file" "$dest_file"; then
            log_info "Modified file detected: $filename"
            changes_detected=true
        fi
    done
    
    # Check for removed files
    if [ -d "$DOC_WEB_DIR/dist" ]; then
        local dest_files=($(find "$DOC_WEB_DIR/dist" -name "*.apib" -type f))
        for dest_file in "${dest_files[@]}"; do
            local filename=$(basename "$dest_file")
            local source_file="$SOURCE_DOC_DIR/$filename"
            
            if [ ! -f "$source_file" ]; then
                log_info "Removed file detected: $filename"
                changes_detected=true
            fi
        done
    fi
    
    if [ "$changes_detected" = true ]; then
        log_info "Changes detected in documentation files"
        return 0
    else
        log_info "No changes detected in documentation files"
        return 1
    fi
}

# Function to update documentation files
update_files() {
    log_info "Updating documentation files..."
    
    if [ "$DRY_RUN" = true ]; then
        log_info "[DRY RUN] Would copy files from $SOURCE_DOC_DIR to $DOC_WEB_DIR/dist"
        
        # Show what would be copied
        local source_files=($(find "$SOURCE_DOC_DIR" -name "*.apib" -type f))
        for source_file in "${source_files[@]}"; do
            local filename=$(basename "$source_file")
            log_info "[DRY RUN] Would copy: $filename"
        done
        
        return 0
    fi
    
    # Remove old .apib files
    rm -f "$DOC_WEB_DIR/dist"/*.apib 2>/dev/null || true
    
    # Copy new files
    local copied_count=0
    local source_files=($(find "$SOURCE_DOC_DIR" -name "*.apib" -type f))
    
    for source_file in "${source_files[@]}"; do
        local filename=$(basename "$source_file")
        cp "$source_file" "$DOC_WEB_DIR/dist/$filename"
        log_info "Copied: $filename"
        ((copied_count++))
    done
    
    # Set ownership
    chown -R "$APP_USER:$APP_USER" "$DOC_WEB_DIR/dist"
    
    log_success "Updated $copied_count documentation files"
}

# Function to restart service
restart_service() {
    if [ "$RESTART_SERVICE" = false ]; then
        log_info "Skipping service restart (--no-restart specified)"
        return 0
    fi
    
    if [ "$DRY_RUN" = true ]; then
        log_info "[DRY RUN] Would restart doc_web service"
        return 0
    fi
    
    log_info "Restarting doc_web service..."
    
    if [ -f "$SCRIPTS_DIR/DOC_WEB.sh" ]; then
        if "$SCRIPTS_DIR/DOC_WEB.sh" restart; then
            log_success "Doc_web service restarted successfully"
            
            # Wait a moment and test
            sleep 3
            if "$SCRIPTS_DIR/DOC_WEB.sh" test; then
                log_success "Service is responding correctly"
            else
                log_warning "Service restarted but may not be responding correctly"
            fi
        else
            log_error "Failed to restart doc_web service"
            return 1
        fi
    else
        log_warning "Service script not found: $SCRIPTS_DIR/DOC_WEB.sh"
        log_info "You may need to manually restart the service"
    fi
}

# Function to show summary
show_summary() {
    log_success "Documentation update completed!"
    echo ""
    echo "Summary:"
    echo "  Source: $SOURCE_DOC_DIR"
    echo "  Destination: $DOC_WEB_DIR/dist"
    echo "  Files updated: $(find "$DOC_WEB_DIR/dist" -name "*.apib" -type f | wc -l)"
    echo ""
    
    if [ "$RESTART_SERVICE" = true ] && [ "$DRY_RUN" = false ]; then
        echo "Service status:"
        "$SCRIPTS_DIR/DOC_WEB.sh" status 2>/dev/null || echo "  Unable to check service status"
        echo ""
    fi
    
    echo "You can now access the updated documentation at:"
    echo "  http://your-domain.com/docs"
    echo ""
    
    if [ "$CREATE_BACKUP" = true ]; then
        echo "Backup created in: $DOC_WEB_DIR/dist.backup.*"
        echo ""
    fi
}

# Main function
main() {
    log_info "Starting documentation update process..."
    
    check_directories
    create_backup
    
    # Check for changes unless force update is specified
    if [ "$FORCE_UPDATE" = false ]; then
        if ! check_changes; then
            log_info "No changes detected. Use --force to update anyway."
            exit 0
        fi
    else
        log_info "Force update specified, skipping change detection"
    fi
    
    update_files
    restart_service
    show_summary
}

# Run main function
main "$@"
