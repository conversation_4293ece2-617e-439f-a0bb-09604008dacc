package controllers;

import com.fasterxml.jackson.databind.node.ArrayNode;
import com.fasterxml.jackson.databind.node.ObjectNode;
import play.libs.Json;
import play.mvc.Controller;
import play.mvc.Result;
import services.ApiDocumentationService;
import views.html.documentation_index;
import views.html.documentation_view;

import javax.inject.Inject;
import java.io.File;
import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.util.*;

/**
 * Documentation Controller
 *
 * Provides API documentation functionality similar to the doc_web Node.js application
 * but integrated directly into the Play Framework application.
 *
 * Features:
 * - Lists all available API documentation files
 * - Renders individual documentation pages
 * - Supports API Blueprint (.apib) files
 * - Provides JSON API for documentation metadata
 */
public class DocumentationController extends Controller {

    private static final String DOC_DIRECTORY = "doc";
    private static final String DOC_EXTENSION = ".apib";

    @Inject
    private ApiDocumentationService documentationService;

    /**
     * GET /docs
     *
     * Renders the documentation index page with a list of all available documentation files
     */
    public Result index() {
        try {
            List<DocumentInfo> docs = getAvailableDocuments();
            return ok(documentation_index.render("LAVOMAT - API Documentation", docs));
        } catch (Exception e) {
            return internalServerError("Error loading documentation: " + e.getMessage());
        }
    }

    /**
     * GET /docs/{docName}
     *
     * Renders a specific documentation file
     */
    public Result viewDocument(String docName) {
        try {
            // Sanitize the document name to prevent path traversal
            String sanitizedDocName = sanitizeDocumentName(docName);

            // Find the document
            Optional<DocumentInfo> docInfo = getDocumentByName(sanitizedDocName);
            if (!docInfo.isPresent()) {
                return notFound("Documentation not found: " + docName);
            }

            // Read the document content
            String content = readDocumentContent(docInfo.get());

            // Check if this is an API Blueprint file and render accordingly
            if (docInfo.get().getFilename().endsWith(".apib")) {
                // Render API Blueprint to HTML
                String renderedHtml = documentationService.renderToHtml(content, docInfo.get().getDisplayName());
                return ok(renderedHtml).as("text/html; charset=utf-8");
            } else {
                // For other file types, show raw content
                return ok(documentation_view.render(
                    "LAVOMAT - " + docInfo.get().getDisplayName(),
                    docInfo.get().getDisplayName(),
                    content
                ));
            }

        } catch (Exception e) {
            return internalServerError("Error loading document: " + e.getMessage());
        }
    }

    /**
     * GET /api/v1/docs
     *
     * Returns JSON list of available documentation
     */
    public Result listDocumentsApi() {
        try {
            List<DocumentInfo> docs = getAvailableDocuments();
            ArrayNode jsonArray = Json.newArray();

            for (DocumentInfo doc : docs) {
                ObjectNode docJson = Json.newObject();
                docJson.put("name", doc.getDisplayName());
                docJson.put("url", doc.getUrl());
                docJson.put("filename", doc.getFilename());
                jsonArray.add(docJson);
            }

            ObjectNode result = Json.newObject();
            result.set("docs", jsonArray);
            result.put("count", docs.size());

            return ok(result);

        } catch (Exception e) {
            ObjectNode error = Json.newObject();
            error.put("error", "Error loading documentation");
            error.put("message", e.getMessage());
            return internalServerError(error);
        }
    }

    /**
     * GET /api/v1/docs/{docName}
     *
     * Returns JSON metadata for a specific document
     */
    public Result getDocumentApi(String docName) {
        try {
            String sanitizedDocName = sanitizeDocumentName(docName);
            Optional<DocumentInfo> docInfo = getDocumentByName(sanitizedDocName);

            if (!docInfo.isPresent()) {
                ObjectNode error = Json.newObject();
                error.put("error", "Document not found");
                error.put("document", docName);
                return notFound(error);
            }

            DocumentInfo doc = docInfo.get();
            ObjectNode result = Json.newObject();
            result.put("name", doc.getDisplayName());
            result.put("url", doc.getUrl());
            result.put("filename", doc.getFilename());

            return ok(result);

        } catch (Exception e) {
            ObjectNode error = Json.newObject();
            error.put("error", "Error loading document metadata");
            error.put("message", e.getMessage());
            return internalServerError(error);
        }
    }

    /**
     * GET /docs/{docName}/raw
     *
     * Returns the raw content of a documentation file
     */
    public Result getRawDocument(String docName) {
        try {
            String sanitizedDocName = sanitizeDocumentName(docName);
            Optional<DocumentInfo> docInfo = getDocumentByName(sanitizedDocName);

            if (!docInfo.isPresent()) {
                return notFound("Documentation not found: " + docName);
            }

            String content = readDocumentContent(docInfo.get());
            return ok(content).as("text/plain; charset=utf-8");

        } catch (Exception e) {
            return internalServerError("Error reading document: " + e.getMessage());
        }
    }

    // Helper methods

    /**
     * Gets all available documentation files
     */
    private List<DocumentInfo> getAvailableDocuments() {
        List<DocumentInfo> result = new ArrayList<DocumentInfo>();
        Path docPath = Paths.get(DOC_DIRECTORY);

        if (!Files.exists(docPath) || !Files.isDirectory(docPath)) {
            return result;
        }

        try {
            File[] files = docPath.toFile().listFiles();
            if (files != null) {
                for (File file : files) {
                    if (file.isFile() && file.getName().endsWith(DOC_EXTENSION)) {
                        DocumentInfo docInfo = createDocumentInfo(file.toPath());
                        if (docInfo != null) {
                            result.add(docInfo);
                        }
                    }
                }

                // Sort by display name
                Collections.sort(result, new Comparator<DocumentInfo>() {
                    @Override
                    public int compare(DocumentInfo a, DocumentInfo b) {
                        return a.getDisplayName().compareTo(b.getDisplayName());
                    }
                });
            }
        } catch (Exception e) {
            play.Logger.error("Error listing documentation files", e);
        }

        return result;
    }

    /**
     * Creates DocumentInfo from a file path
     */
    private DocumentInfo createDocumentInfo(Path filePath) {
        try {
            String filename = filePath.getFileName().toString();
            String nameWithoutExtension = filename.substring(0, filename.lastIndexOf('.'));
            String displayName = nameWithoutExtension.replace('_', '/');
            String url = "/docs/" + nameWithoutExtension;

            return new DocumentInfo(filename, displayName, url, filePath);

        } catch (Exception e) {
            // Log error and return null to filter out problematic files
            play.Logger.error("Error reading file info for: " + filePath, e);
            return null;
        }
    }

    /**
     * Finds a document by its name
     */
    private Optional<DocumentInfo> getDocumentByName(String docName) {
        List<DocumentInfo> docs = getAvailableDocuments();
        for (DocumentInfo doc : docs) {
            if (doc.getUrl().equals("/docs/" + docName)) {
                return Optional.of(doc);
            }
        }
        return Optional.empty();
    }

    /**
     * Reads the content of a documentation file
     */
    private String readDocumentContent(DocumentInfo docInfo) throws IOException {
        return new String(Files.readAllBytes(docInfo.getFilePath()), "UTF-8");
    }

    /**
     * Sanitizes document name to prevent path traversal attacks
     */
    private String sanitizeDocumentName(String docName) {
        if (docName == null) {
            return "";
        }

        // Remove any path separators and dangerous characters
        return docName.replaceAll("[^a-zA-Z0-9_-]", "");
    }

    /**
     * Inner class to hold document information
     */
    public static class DocumentInfo {
        private final String filename;
        private final String displayName;
        private final String url;
        private final Path filePath;

        public DocumentInfo(String filename, String displayName, String url, Path filePath) {
            this.filename = filename;
            this.displayName = displayName;
            this.url = url;
            this.filePath = filePath;
        }

        // Getters
        public String getFilename() {
            return filename;
        }

        public String getDisplayName() {
            return displayName;
        }

        public String getUrl() {
            return url;
        }

        public Path getFilePath() {
            return filePath;
        }
    }
}
