@(title: String, docs: List[controllers.DocumentationController.DocumentInfo])

<!DOCTYPE html>
<html lang="en">
    <head>
        <meta charset="UTF-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>@title</title>
        <style>
            @@import

            url
            (
            'https://fonts.googleapis.com/css?family=Roboto:400,700|Inconsolata|Raleway:200'
            );

            body {
                color: #333;
                background: #f8f9fa;
                font: 400 16px / 1.6 'Roboto', Helvetica, sans-serif;
                margin: 0;
                padding: 0;
            }

            .header {
                background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
                color: white;
                padding: 2rem 0;
                text-align: center;
                box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
            }

            .header h1 {
                font: 200 2.5rem 'Raleway', Helvetica, sans-serif;
                margin: 0;
                text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
            }

            .header p {
                margin: 0.5rem 0 0 0;
                opacity: 0.9;
                font-size: 1.1rem;
            }

            .container {
                max-width: 1200px;
                margin: 0 auto;
                padding: 2rem;
            }

            .docs-grid {
                display: grid;
                grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
                gap: 1.5rem;
                margin-top: 2rem;
            }

            .doc-card {
                background: white;
                border-radius: 8px;
                padding: 1.5rem;
                box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
                transition: all 0.3s ease;
                border-left: 4px solid #667eea;
            }

            .doc-card:hover {
                transform: translateY(-2px);
                box-shadow: 0 4px 16px rgba(0, 0, 0, 0.15);
            }

            .doc-card h3 {
                margin: 0 0 0.5rem 0;
                color: #333;
                font-size: 1.3rem;
            }

            .doc-card h3 a {
                color: #333;
                text-decoration: none;
                transition: color 0.3s ease;
            }

            .doc-card h3 a:hover {
                color: #667eea;
            }

            .doc-meta {
                color: #666;
                font-size: 0.9rem;
                margin-top: 0.5rem;
            }

            .doc-meta span {
                margin-right: 1rem;
            }

            .no-docs {
                text-align: center;
                padding: 3rem;
                color: #666;
            }

            .no-docs h3 {
                color: #999;
                font-size: 1.5rem;
                margin-bottom: 1rem;
            }

            .stats {
                background: white;
                border-radius: 8px;
                padding: 1rem 1.5rem;
                margin-bottom: 2rem;
                box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
                display: flex;
                justify-content: space-between;
                align-items: center;
            }

            .stats-item {
                text-align: center;
            }

            .stats-number {
                font-size: 2rem;
                font-weight: bold;
                color: #667eea;
            }

            .stats-label {
                color: #666;
                font-size: 0.9rem;
            }

            .api-link {
                background: #667eea;
                color: white;
                padding: 0.5rem 1rem;
                border-radius: 4px;
                text-decoration: none;
                font-size: 0.9rem;
                transition: background 0.3s ease;
            }

            .api-link:hover {
                background: #5a6fd8;
                color: white;
            }

            .search-box {
                width: 100%;
                padding: 0.75rem;
                border: 2px solid #e9ecef;
                border-radius: 4px;
                font-size: 1rem;
                margin-bottom: 1rem;
                transition: border-color 0.3s ease;
            }

            .search-box:focus {
                outline: none;
                border-color: #667eea;
            }

            @@media

            (
            max-width:

            768
            px

            )
            {
            .docs-grid {
                grid-template-columns: 1fr;
            }

            .stats {
                flex-direction: column;
                gap: 1rem;
            }

            .container {
                padding: 1rem;
            }

            }
        </style>
    </head>
    <body>
        <div class="header">
            <h1>LAVOMAT API Documentation</h1>
            <p>Comprehensive API documentation for all LAVOMAT services</p>
        </div>

        <div class="container">
            <div class="stats">
                <div class="stats-item">
                    <div class="stats-number">@docs.size()</div>
                    <div class="stats-label">Documentation Files</div>
                </div>
                <div class="stats-item">
                    <div class="stats-label">Status</div>
                    <div class="stats-number" style="font-size: 1rem;">
                    @if(docs.size() > 0) {
                        Ready
                    } else {
                        No Docs
                    }
                    </div>
                </div>
                <a href="/api/v1/docs" class="api-link">JSON API</a>
            </div>

            @if(docs.size() > 0) {
                <input type="text" class="search-box" id="searchBox" placeholder="Search documentation...">

                <div class="docs-grid" id="docsGrid">
                @for(doc <- docs) {
                    <div class="doc-card" data-name="@doc.getDisplayName().toLowerCase()">
                        <h3>
                            <a href="@doc.getUrl()">@doc.getDisplayName()</a>
                        </h3>
                        <div class="doc-meta">
                            <span>📄 @doc.getFilename()</span>
                            <span>📊 @{
                                String.format("%.1f KB", doc.getSize() / 1024.0)
                            }</span>
                        </div>
                    </div>
                }
                </div>
            } else {
                <div class="no-docs">
                    <h3>No Documentation Found</h3>
                    <p>No API documentation files are currently available.</p>
                    <p>Documentation files should be placed in the <code>doc/</code> directory with <code>.apib</code>
                        extension.</p>
                </div>
            }
        </div>

        <script>
            // Simple search functionality
            document.getElementById('searchBox').addEventListener('input', function (e) {
                const searchTerm = e.target.value.toLowerCase();
                const docCards = document.querySelectorAll('.doc-card');

                docCards.forEach(card => {
                    const docName = card.getAttribute('data-name');
                    if (docName.includes(searchTerm)) {
                        card.style.display = 'block';
                    } else {
                        card.style.display = 'none';
                    }
                });
            });

            // Add click tracking for analytics (optional)
            document.querySelectorAll('.doc-card a').forEach(link => {
                link.addEventListener('click', function (e) {
                    // You can add analytics tracking here
                    console.log('Documentation accessed:', this.textContent);
                });
            });
        </script>
    </body>
</html>
