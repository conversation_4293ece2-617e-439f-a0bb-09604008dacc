@(title: String, docName: String, content: String)

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>@title</title>
    <style>
        @@import url('https://fonts.googleapis.com/css?family=Roboto:400,700|Inconsolata|Raleway:200');

        body {
            color: #333;
            background: #f8f9fa;
            font: 400 16px / 1.6 'Roboto', Helvetica, sans-serif;
            margin: 0;
            padding: 0;
        }

        .header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 1.5rem 0;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }

        .header-content {
            max-width: 1200px;
            margin: 0 auto;
            padding: 0 2rem;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .header h1 {
            font: 200 2rem 'Raleway', Helvetica, sans-serif;
            margin: 0;
            text-shadow: 0 2px 4px rgba(0,0,0,0.3);
        }

        .back-link {
            background: rgba(255,255,255,0.2);
            color: white;
            padding: 0.5rem 1rem;
            border-radius: 4px;
            text-decoration: none;
            transition: background 0.3s ease;
        }

        .back-link:hover {
            background: rgba(255,255,255,0.3);
            color: white;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 2rem;
        }

        .doc-actions {
            background: white;
            border-radius: 8px;
            padding: 1rem 1.5rem;
            margin-bottom: 2rem;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
            display: flex;
            justify-content: space-between;
            align-items: center;
            flex-wrap: wrap;
            gap: 1rem;
        }

        .doc-info {
            display: flex;
            align-items: center;
            gap: 1rem;
        }

        .doc-title {
            font-size: 1.2rem;
            font-weight: bold;
            color: #333;
        }

        .action-buttons {
            display: flex;
            gap: 0.5rem;
        }

        .btn {
            padding: 0.5rem 1rem;
            border-radius: 4px;
            text-decoration: none;
            font-size: 0.9rem;
            transition: all 0.3s ease;
            border: none;
            cursor: pointer;
        }

        .btn-primary {
            background: #667eea;
            color: white;
        }

        .btn-primary:hover {
            background: #5a6fd8;
            color: white;
        }

        .btn-secondary {
            background: #6c757d;
            color: white;
        }

        .btn-secondary:hover {
            background: #5a6268;
            color: white;
        }

        .content-container {
            background: white;
            border-radius: 8px;
            padding: 2rem;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
            min-height: 500px;
        }

        .raw-content {
            font-family: 'Inconsolata', monospace;
            font-size: 14px;
            line-height: 1.5;
            white-space: pre-wrap;
            word-wrap: break-word;
            background: #f8f9fa;
            padding: 1.5rem;
            border-radius: 4px;
            border: 1px solid #e9ecef;
            overflow-x: auto;
        }

        .content-notice {
            background: #e3f2fd;
            border: 1px solid #bbdefb;
            border-radius: 4px;
            padding: 1rem;
            margin-bottom: 1.5rem;
            color: #1565c0;
        }

        .content-notice strong {
            color: #0d47a1;
        }

        .line-numbers {
            background: #f1f3f4;
            color: #666;
            padding: 1.5rem 0.5rem;
            border-radius: 4px 0 0 4px;
            border-right: 1px solid #e9ecef;
            font-family: 'Inconsolata', monospace;
            font-size: 12px;
            line-height: 1.5;
            text-align: right;
            user-select: none;
            min-width: 3rem;
        }

        .content-with-lines {
            display: flex;
            background: #f8f9fa;
            border-radius: 4px;
            border: 1px solid #e9ecef;
            overflow: hidden;
        }

        .content-lines {
            flex: 1;
            font-family: 'Inconsolata', monospace;
            font-size: 14px;
            line-height: 1.5;
            white-space: pre-wrap;
            word-wrap: break-word;
            padding: 1.5rem;
            overflow-x: auto;
        }

        .search-in-doc {
            width: 100%;
            padding: 0.75rem;
            border: 2px solid #e9ecef;
            border-radius: 4px;
            font-size: 1rem;
            margin-bottom: 1rem;
            transition: border-color 0.3s ease;
        }

        .search-in-doc:focus {
            outline: none;
            border-color: #667eea;
        }

        .highlight {
            background-color: #fff3cd;
            padding: 0.1rem 0.2rem;
            border-radius: 2px;
        }

        @@media (max-width: 768px) {
            .header-content {
                flex-direction: column;
                gap: 1rem;
                text-align: center;
            }
            
            .doc-actions {
                flex-direction: column;
                align-items: stretch;
            }
            
            .action-buttons {
                justify-content: center;
            }
            
            .container {
                padding: 1rem;
            }
            
            .content-with-lines {
                flex-direction: column;
            }
            
            .line-numbers {
                display: none;
            }
        }

        @@media print {
            .header, .doc-actions, .search-in-doc {
                display: none !important;
            }
            
            body {
                background: white;
            }
            
            .content-container {
                box-shadow: none;
                border: none;
            }
        }
    </style>
</head>
<body>
    <div class="header">
        <div class="header-content">
            <h1>@docName</h1>
            <a href="/docs" class="back-link">← Back to Documentation</a>
        </div>
    </div>

    <div class="container">
        <div class="doc-actions">
            <div class="doc-info">
                <div class="doc-title">@docName</div>
            </div>
            <div class="action-buttons">
                <a href="@{"/docs/" + docName.replaceAll("/", "_") + "/raw"}" class="btn btn-secondary" target="_blank">View Raw</a>
                <button onclick="window.print()" class="btn btn-primary">Print</button>
                <button onclick="copyToClipboard()" class="btn btn-secondary">Copy Content</button>
            </div>
        </div>

        <div class="content-container">
            <div class="content-notice">
                <strong>Note:</strong> This is the raw API Blueprint content. In a full implementation, this would be rendered as formatted HTML documentation using a library like Aglio or similar API Blueprint processor.
            </div>

            <input type="text" class="search-in-doc" id="searchInDoc" placeholder="Search in this document...">

            <div class="content-with-lines">
                <div class="line-numbers" id="lineNumbers"></div>
                <div class="content-lines" id="contentLines">@content</div>
            </div>
        </div>
    </div>

    <script>
        // Generate line numbers
        function generateLineNumbers() {
            const content = document.getElementById('contentLines').textContent;
            const lines = content.split('\n');
            const lineNumbers = document.getElementById('lineNumbers');
            
            let numbersHtml = '';
            for (let i = 1; i <= lines.length; i++) {
                numbersHtml += i + '\n';
            }
            lineNumbers.textContent = numbersHtml;
        }

        // Search functionality
        function highlightSearchTerm(searchTerm) {
            const contentElement = document.getElementById('contentLines');
            const originalContent = contentElement.getAttribute('data-original') || contentElement.textContent;
            
            if (!contentElement.getAttribute('data-original')) {
                contentElement.setAttribute('data-original', originalContent);
            }

            if (!searchTerm) {
                contentElement.textContent = originalContent;
                return;
            }

            const regex = new RegExp(`(${searchTerm.replace(/[.*+?^${}()|[\]\\]/g, '\\$&')})`, 'gi');
            const highlightedContent = originalContent.replace(regex, '<span class="highlight">$1</span>');
            contentElement.innerHTML = highlightedContent;
        }

        // Copy to clipboard functionality
        function copyToClipboard() {
            const content = document.getElementById('contentLines').getAttribute('data-original') || 
                           document.getElementById('contentLines').textContent;
            
            navigator.clipboard.writeText(content).then(function() {
                // Show temporary success message
                const button = event.target;
                const originalText = button.textContent;
                button.textContent = 'Copied!';
                button.style.background = '#28a745';
                
                setTimeout(() => {
                    button.textContent = originalText;
                    button.style.background = '';
                }, 2000);
            }).catch(function(err) {
                console.error('Could not copy text: ', err);
                alert('Failed to copy to clipboard');
            });
        }

        // Initialize
        document.addEventListener('DOMContentLoaded', function() {
            generateLineNumbers();
            
            // Search functionality
            document.getElementById('searchInDoc').addEventListener('input', function(e) {
                highlightSearchTerm(e.target.value);
            });
        });

        // Keyboard shortcuts
        document.addEventListener('keydown', function(e) {
            // Ctrl+F or Cmd+F to focus search
            if ((e.ctrlKey || e.metaKey) && e.key === 'f') {
                e.preventDefault();
                document.getElementById('searchInDoc').focus();
            }
            
            // Escape to clear search
            if (e.key === 'Escape') {
                const searchBox = document.getElementById('searchInDoc');
                searchBox.value = '';
                highlightSearchTerm('');
            }
        });
    </script>
</body>
</html>
