package services;

import java.io.*;
import java.nio.file.*;
import java.util.*;
import java.util.regex.*;
import java.util.stream.Collectors;

/**
 * API Documentation Service
 * 
 * Provides functionality to parse and render API Blueprint (.apib) files
 * into HTML format, similar to what Aglio does in the Node.js ecosystem.
 * 
 * This is a simplified implementation that handles basic API Blueprint syntax.
 * For full API Blueprint support, you might want to integrate with a Java-based
 * markdown processor or call external tools.
 */
public class ApiDocumentationService {
    
    private static final Pattern TITLE_PATTERN = Pattern.compile("^# (.+)$", Pattern.MULTILINE);
    private static final Pattern HOST_PATTERN = Pattern.compile("^HOST: (.+)$", Pattern.MULTILINE);
    private static final Pattern ENDPOINT_PATTERN = Pattern.compile("^### (.+) \\[([A-Z]+) (.+)\\]$", Pattern.MULTILINE);
    private static final Pattern DESCRIPTION_PATTERN = Pattern.compile("^- (.+)$", Pattern.MULTILINE);
    private static final Pattern PARAMETER_PATTERN = Pattern.compile("^\\+ (.+)$", Pattern.MULTILINE);
    private static final Pattern RESPONSE_PATTERN = Pattern.compile("^\\+ Response (\\d+) \\((.+)\\)$", Pattern.MULTILINE);
    
    /**
     * Renders an API Blueprint file to HTML
     */
    public String renderToHtml(String apibContent, String title) {
        try {
            ApiBlueprint blueprint = parseApiBlueprint(apibContent);
            return generateHtml(blueprint, title);
        } catch (Exception e) {
            play.Logger.error("Error rendering API Blueprint to HTML", e);
            return generateErrorHtml("Error rendering documentation: " + e.getMessage(), apibContent);
        }
    }
    
    /**
     * Parses API Blueprint content into a structured format
     */
    private ApiBlueprint parseApiBlueprint(String content) {
        ApiBlueprint blueprint = new ApiBlueprint();
        
        // Extract title
        Matcher titleMatcher = TITLE_PATTERN.matcher(content);
        if (titleMatcher.find()) {
            blueprint.title = titleMatcher.group(1);
        }
        
        // Extract host
        Matcher hostMatcher = HOST_PATTERN.matcher(content);
        if (hostMatcher.find()) {
            blueprint.host = hostMatcher.group(1);
        }
        
        // Extract endpoints
        blueprint.endpoints = parseEndpoints(content);
        
        return blueprint;
    }
    
    /**
     * Parses endpoints from the API Blueprint content
     */
    private List<Endpoint> parseEndpoints(String content) {
        List<Endpoint> endpoints = new ArrayList<>();
        String[] sections = content.split("(?=^### )", Pattern.MULTILINE);
        
        for (String section : sections) {
            if (section.trim().isEmpty()) continue;
            
            Matcher endpointMatcher = ENDPOINT_PATTERN.matcher(section);
            if (endpointMatcher.find()) {
                Endpoint endpoint = new Endpoint();
                endpoint.title = endpointMatcher.group(1);
                endpoint.method = endpointMatcher.group(2);
                endpoint.path = endpointMatcher.group(3);
                
                // Parse description
                endpoint.description = parseDescription(section);
                
                // Parse parameters
                endpoint.parameters = parseParameters(section);
                
                // Parse responses
                endpoint.responses = parseResponses(section);
                
                endpoints.add(endpoint);
            }
        }
        
        return endpoints;
    }
    
    private String parseDescription(String section) {
        StringBuilder description = new StringBuilder();
        String[] lines = section.split("\n");
        boolean inDescription = false;
        
        for (String line : lines) {
            if (line.startsWith("### ")) {
                inDescription = true;
                continue;
            }
            if (line.startsWith("+ ") || line.startsWith("    +")) {
                break;
            }
            if (inDescription && !line.trim().isEmpty() && !line.startsWith("#")) {
                description.append(line.trim()).append("\n");
            }
        }
        
        return description.toString().trim();
    }
    
    private List<String> parseParameters(String section) {
        List<String> parameters = new ArrayList<>();
        Matcher paramMatcher = PARAMETER_PATTERN.matcher(section);
        
        while (paramMatcher.find()) {
            String param = paramMatcher.group(1);
            if (!param.startsWith("Response")) {
                parameters.add(param);
            }
        }
        
        return parameters;
    }
    
    private List<Response> parseResponses(String section) {
        List<Response> responses = new ArrayList<>();
        Matcher responseMatcher = RESPONSE_PATTERN.matcher(section);
        
        while (responseMatcher.find()) {
            Response response = new Response();
            response.statusCode = responseMatcher.group(1);
            response.contentType = responseMatcher.group(2);
            responses.add(response);
        }
        
        return responses;
    }
    
    /**
     * Generates HTML from the parsed API Blueprint
     */
    private String generateHtml(ApiBlueprint blueprint, String pageTitle) {
        StringBuilder html = new StringBuilder();
        
        html.append("<!DOCTYPE html>\n");
        html.append("<html lang=\"en\">\n");
        html.append("<head>\n");
        html.append("    <meta charset=\"UTF-8\">\n");
        html.append("    <meta name=\"viewport\" content=\"width=device-width, initial-scale=1.0\">\n");
        html.append("    <title>").append(escapeHtml(pageTitle)).append("</title>\n");
        html.append(getDocumentationStyles());
        html.append("</head>\n");
        html.append("<body>\n");
        
        // Header
        html.append("    <div class=\"header\">\n");
        html.append("        <div class=\"header-content\">\n");
        html.append("            <h1>").append(escapeHtml(blueprint.title != null ? blueprint.title : pageTitle)).append("</h1>\n");
        if (blueprint.host != null) {
            html.append("            <div class=\"host\">Base URL: <code>").append(escapeHtml(blueprint.host)).append("</code></div>\n");
        }
        html.append("            <a href=\"/docs\" class=\"back-link\">← Back to Documentation</a>\n");
        html.append("        </div>\n");
        html.append("    </div>\n");
        
        // Navigation
        if (!blueprint.endpoints.isEmpty()) {
            html.append("    <div class=\"nav-container\">\n");
            html.append("        <nav class=\"endpoint-nav\">\n");
            html.append("            <h3>Endpoints</h3>\n");
            html.append("            <ul>\n");
            for (int i = 0; i < blueprint.endpoints.size(); i++) {
                Endpoint endpoint = blueprint.endpoints.get(i);
                html.append("                <li>\n");
                html.append("                    <a href=\"#endpoint-").append(i).append("\">\n");
                html.append("                        <span class=\"method method-").append(endpoint.method.toLowerCase()).append("\">").append(endpoint.method).append("</span>\n");
                html.append("                        <span class=\"path\">").append(escapeHtml(endpoint.path)).append("</span>\n");
                html.append("                    </a>\n");
                html.append("                </li>\n");
            }
            html.append("            </ul>\n");
            html.append("        </nav>\n");
            html.append("    </div>\n");
        }
        
        // Content
        html.append("    <div class=\"container\">\n");
        
        // Endpoints
        for (int i = 0; i < blueprint.endpoints.size(); i++) {
            Endpoint endpoint = blueprint.endpoints.get(i);
            html.append("        <div class=\"endpoint\" id=\"endpoint-").append(i).append("\">\n");
            html.append("            <div class=\"endpoint-header\">\n");
            html.append("                <h2>").append(escapeHtml(endpoint.title)).append("</h2>\n");
            html.append("                <div class=\"endpoint-signature\">\n");
            html.append("                    <span class=\"method method-").append(endpoint.method.toLowerCase()).append("\">").append(endpoint.method).append("</span>\n");
            html.append("                    <span class=\"path\">").append(escapeHtml(endpoint.path)).append("</span>\n");
            html.append("                </div>\n");
            html.append("            </div>\n");
            
            if (!endpoint.description.isEmpty()) {
                html.append("            <div class=\"endpoint-description\">\n");
                html.append("                <p>").append(escapeHtml(endpoint.description).replace("\n", "<br>")).append("</p>\n");
                html.append("            </div>\n");
            }
            
            if (!endpoint.parameters.isEmpty()) {
                html.append("            <div class=\"endpoint-section\">\n");
                html.append("                <h3>Parameters</h3>\n");
                html.append("                <ul class=\"parameter-list\">\n");
                for (String param : endpoint.parameters) {
                    html.append("                    <li>").append(escapeHtml(param)).append("</li>\n");
                }
                html.append("                </ul>\n");
                html.append("            </div>\n");
            }
            
            if (!endpoint.responses.isEmpty()) {
                html.append("            <div class=\"endpoint-section\">\n");
                html.append("                <h3>Responses</h3>\n");
                for (Response response : endpoint.responses) {
                    html.append("                <div class=\"response\">\n");
                    html.append("                    <div class=\"response-header\">\n");
                    html.append("                        <span class=\"status-code status-").append(response.statusCode.charAt(0)).append("xx\">").append(response.statusCode).append("</span>\n");
                    html.append("                        <span class=\"content-type\">").append(escapeHtml(response.contentType)).append("</span>\n");
                    html.append("                    </div>\n");
                    html.append("                </div>\n");
                }
                html.append("            </div>\n");
            }
            
            html.append("        </div>\n");
        }
        
        html.append("    </div>\n");
        html.append("</body>\n");
        html.append("</html>\n");
        
        return html.toString();
    }
    
    /**
     * Generates error HTML when rendering fails
     */
    private String generateErrorHtml(String errorMessage, String originalContent) {
        StringBuilder html = new StringBuilder();
        html.append("<!DOCTYPE html>\n<html><head><title>Documentation Error</title></head><body>\n");
        html.append("<h1>Documentation Rendering Error</h1>\n");
        html.append("<p>").append(escapeHtml(errorMessage)).append("</p>\n");
        html.append("<h2>Original Content:</h2>\n");
        html.append("<pre>").append(escapeHtml(originalContent)).append("</pre>\n");
        html.append("</body></html>");
        return html.toString();
    }
    
    /**
     * Returns CSS styles for the documentation
     */
    private String getDocumentationStyles() {
        return """
            <style>
                @import url('https://fonts.googleapis.com/css?family=Roboto:400,700|Inconsolata|Raleway:200');
                
                body { margin: 0; font-family: 'Roboto', sans-serif; background: #f8f9fa; color: #333; }
                .header { background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; padding: 2rem 0; }
                .header-content { max-width: 1200px; margin: 0 auto; padding: 0 2rem; display: flex; justify-content: space-between; align-items: center; }
                .header h1 { margin: 0; font-size: 2.5rem; font-weight: 200; }
                .host { margin-top: 0.5rem; opacity: 0.9; }
                .back-link { background: rgba(255,255,255,0.2); color: white; padding: 0.5rem 1rem; border-radius: 4px; text-decoration: none; }
                .nav-container { background: white; border-bottom: 1px solid #e9ecef; }
                .endpoint-nav { max-width: 1200px; margin: 0 auto; padding: 1rem 2rem; }
                .endpoint-nav h3 { margin: 0 0 1rem 0; }
                .endpoint-nav ul { list-style: none; padding: 0; margin: 0; }
                .endpoint-nav li { margin-bottom: 0.5rem; }
                .endpoint-nav a { text-decoration: none; display: flex; align-items: center; gap: 0.5rem; }
                .container { max-width: 1200px; margin: 0 auto; padding: 2rem; }
                .endpoint { background: white; border-radius: 8px; margin-bottom: 2rem; box-shadow: 0 2px 8px rgba(0,0,0,0.1); }
                .endpoint-header { padding: 1.5rem; border-bottom: 1px solid #e9ecef; }
                .endpoint-header h2 { margin: 0 0 1rem 0; }
                .endpoint-signature { display: flex; align-items: center; gap: 1rem; }
                .method { padding: 0.25rem 0.75rem; border-radius: 4px; font-weight: bold; font-size: 0.9rem; }
                .method-get { background: #d4edda; color: #155724; }
                .method-post { background: #cce5ff; color: #004085; }
                .method-put { background: #fff3cd; color: #856404; }
                .method-delete { background: #f8d7da; color: #721c24; }
                .path { font-family: 'Inconsolata', monospace; background: #f8f9fa; padding: 0.25rem 0.5rem; border-radius: 4px; }
                .endpoint-description, .endpoint-section { padding: 1.5rem; }
                .endpoint-section h3 { margin: 0 0 1rem 0; color: #495057; }
                .parameter-list { list-style: none; padding: 0; }
                .parameter-list li { background: #f8f9fa; padding: 0.5rem; margin-bottom: 0.5rem; border-radius: 4px; }
                .response { margin-bottom: 1rem; }
                .response-header { display: flex; align-items: center; gap: 1rem; }
                .status-code { padding: 0.25rem 0.75rem; border-radius: 4px; font-weight: bold; }
                .status-2xx { background: #d4edda; color: #155724; }
                .status-4xx { background: #fff3cd; color: #856404; }
                .status-5xx { background: #f8d7da; color: #721c24; }
                .content-type { font-family: 'Inconsolata', monospace; background: #f8f9fa; padding: 0.25rem 0.5rem; border-radius: 4px; }
                code { background: #f8f9fa; padding: 0.2rem 0.4rem; border-radius: 3px; font-family: 'Inconsolata', monospace; }
            </style>
        """;
    }
    
    /**
     * Escapes HTML characters
     */
    private String escapeHtml(String text) {
        if (text == null) return "";
        return text.replace("&", "&amp;")
                  .replace("<", "&lt;")
                  .replace(">", "&gt;")
                  .replace("\"", "&quot;")
                  .replace("'", "&#39;");
    }
    
    // Data classes
    public static class ApiBlueprint {
        public String title;
        public String host;
        public List<Endpoint> endpoints = new ArrayList<>();
    }
    
    public static class Endpoint {
        public String title;
        public String method;
        public String path;
        public String description = "";
        public List<String> parameters = new ArrayList<>();
        public List<Response> responses = new ArrayList<>();
    }
    
    public static class Response {
        public String statusCode;
        public String contentType;
    }
}
