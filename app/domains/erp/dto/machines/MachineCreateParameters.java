package domains.erp.dto.machines;

import com.fasterxml.jackson.databind.JsonNode;
import global.APIException;
import global.exceptions.MissingParametersException;
import global.exceptions.machines.MachineAlreadyExists;
import models.Building;
import models.Machine;
import models.Machine.MachineType;
import models.MachineModel;
import models.MaintenanceParameter;
import models.Part.PartState;
import org.apache.commons.lang3.StringUtils;
import queries.machine_model.MachineModelQuery;
import queries.machines.MachineQuery;

public class MachineCreateParameters extends dto.JsonBodyActionParameters {

    protected static final String ODOO_ID = "odooId";
    protected static final String NAME = "name";
    protected static final String MODEL = "model";
    protected static final String DESCRIPTION = "description";
    protected static final String SERIAL_NUMBER = "serialNumber";
    protected static final String UNIT_PRICE = "unitPrice";
    protected static final String UY_PRICE = "uyPrice";
    protected static final String AVERAGE_USE_TIME = "averageUseTime";
    protected static final String MACHINE_TYPE = "machineType";
    protected static final String REFERENCE = "reference";
    protected static final String CAPACITY = "capacity";
    protected static final String EXPECTED_USES = "expectedUses";
    protected static final String STATE = "state";

    protected final int odooId;
    protected final String name;
    protected final String model;
    protected final String description;
    protected final String serialNumber;

    protected final double unitPrice;
    protected final double uyPrice;
    protected final int averageUseTime;
    protected final MachineType machineType;
    protected final String reference;
    protected final int capacity;
    protected final int expectedUses;
    protected Building building;
    protected final PartState state;
    MachineModel machineModel;

    Machine machine = null;

    public MachineCreateParameters(JsonNode body) throws APIException {
        this.odooId = safeInt(ODOO_ID, body, 0);
        this.name = safeString(NAME, body, StringUtils.EMPTY);
        this.model = safeString(MODEL, body, StringUtils.EMPTY);
        this.description = safeString(DESCRIPTION, body, StringUtils.EMPTY);
        this.serialNumber = safeString(SERIAL_NUMBER, body, StringUtils.EMPTY);
        this.unitPrice = safeDouble(UNIT_PRICE, body, 0.0);
        this.uyPrice = safeDouble(UY_PRICE, body, 0.0);
        this.averageUseTime = safeInt(AVERAGE_USE_TIME, body, 0);
        this.machineType = safeEnum(MachineType.class, MACHINE_TYPE, body, null);
        this.reference = safeString(REFERENCE, body, StringUtils.EMPTY);
        this.capacity = safeInt(CAPACITY, body, 0);
        this.expectedUses = safeInt(EXPECTED_USES, body, 0);
        this.state = safeEnum(PartState.class, STATE, body, null);
    }

    public MachineCreateParameters validate() throws APIException {
        if (this.odooId <= 0) {
            throw new MissingParametersException(ODOO_ID);
        }

        if (StringUtils.isBlank(this.name)) {
            throw new MissingParametersException(NAME);
        }

        if (StringUtils.isBlank(this.serialNumber)) {
            throw new MissingParametersException(SERIAL_NUMBER);
        }

        if (this.state == null) {
            throw new MissingParametersException(STATE);
        }

        if (this.machineType == null) {
            throw new MissingParametersException(MACHINE_TYPE);
        }

        if (new MachineQuery().filterBySerialNumber(this.serialNumber).any()) {
            throw new MachineAlreadyExists();
        }

        if (new MachineQuery().filterByOdooId(this.odooId).any()) {
            throw new MachineAlreadyExists();
        }

        if (!StringUtils.isBlank(this.model)) {
            this.machineModel = new MachineModelQuery().filterByName(this.model).single();

            if (this.machineModel == null) {
                this.machineModel = new MachineModel(this.model, new MaintenanceParameter());
            }
        }

        this.machine =
            new Machine(this.name, null, this.description, this.state, this.serialNumber);
        this.machine.setOdooId(this.odooId);
        this.machine.setUnitPrice(this.unitPrice);
        this.machine.setUyPrice(this.uyPrice);
        this.machine.setAverageUseTime(this.averageUseTime);
        this.machine.setMachineType(this.machineType);
        this.machine.setReference(this.reference);
        this.machine.setCapacity(this.capacity);
        this.machine.setExpectedUses(this.expectedUses);
        this.machine.setMachineModel(this.machineModel);

        return this;
    }

    public Machine getMachine() {
        return this.machine;
    }
}
